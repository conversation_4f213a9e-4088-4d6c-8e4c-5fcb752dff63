# Realome Theme GSAP Animations

This document explains the GSAP animations added to the Realome theme and how to use them.

## What's Included

### 1. **Automatic Animations**
These animations work automatically without any additional code:

- **Button Hover Effects**: All buttons scale up and get enhanced shadows on hover
- **Scroll Animations**: Content fades in from bottom as you scroll
- **Page Load Animations**: Header and main content animate in when page loads
- **Card Hover Effects**: Property cards and content blocks lift up on hover
- **Link Hover Effects**: Navigation links change color smoothly

### 2. **Button Animations**
All WordPress buttons get these effects:
- Hover: Scale up (1.05x) with enhanced shadow
- Click: Brief scale down (0.95x) then back up
- Smooth transitions with easing

**Affected Elements:**
- `.wp-block-button__link`
- `.wp-element-button`
- `button`
- `input[type="submit"]`
- `input[type="button"]`

### 3. **Scroll-Triggered Animations**
Elements animate in as they come into view:

- **Fade Up**: Headings, paragraphs, images fade in from bottom
- **Scale In**: Images scale from 0.8x to 1x
- **Stagger**: Columns animate in sequence with 0.2s delay

**Affected Elements:**
- `.wp-block-heading`
- `.wp-block-paragraph`
- `.wp-block-image`
- `.wp-block-group`
- `.wp-block-columns`
- `.wp-block-media-text`
- `.wp-block-cover`

### 4. **Real Estate Specific Animations**
Special animations for property-related content:

- **Property Cards**: Enhanced hover with lift and image zoom
- **Price Elements**: Bounce-in animation with back easing
- **Listing Cards**: Smooth hover effects

**Affected Elements:**
- `.property-card`
- `.listing-card`
- `[class*="listing"]`
- `[class*="property"]`
- `.price`
- `.property-price`

## Custom Animation Functions

You can use these JavaScript functions for custom animations:

```javascript
// Slide in from left
RealomeAnimations.slideInLeft(element, delay);

// Slide in from right
RealomeAnimations.slideInRight(element, delay);

// Bounce in effect
RealomeAnimations.bounceIn(element, delay);

// Simple fade in
RealomeAnimations.fadeIn(element, delay);

// Stagger multiple elements
RealomeAnimations.staggerIn(elements, delay);
```

### Example Usage:
```javascript
// Animate a specific element
const myElement = document.querySelector('.my-custom-element');
RealomeAnimations.bounceIn(myElement, 0.5); // 0.5s delay

// Animate multiple elements with stagger
const cards = document.querySelectorAll('.custom-card');
RealomeAnimations.staggerIn(cards, 0.2);
```

## CSS Classes for Animation Control

### Animation-Ready Classes:
- `.animate-on-scroll` - Automatically added to common elements
- `.animated` - Prevents duplicate animations

### Performance Classes:
- `.loaded` - Added to `.wp-site-blocks` when page is ready
- Elements have `will-change` properties for smooth animations

## Accessibility

The animations respect user preferences:
- `prefers-reduced-motion: reduce` disables animations
- Focus states are enhanced for keyboard navigation
- Animations don't interfere with screen readers

## Performance Optimizations

1. **Hardware Acceleration**: Uses `transform` and `opacity` for smooth animations
2. **Will-Change**: Optimizes elements that will be animated
3. **Reduced Motion**: Respects user accessibility preferences
4. **Efficient Selectors**: Targets specific elements to avoid unnecessary processing

## Customization

### Disable Specific Animations:
To disable button animations, add this CSS:
```css
.wp-block-button__link,
.wp-element-button {
    transition: none !important;
}
```

### Modify Animation Speed:
Change duration in the JavaScript file:
```javascript
// In animations.js, change duration values
duration: 0.3, // Make faster: 0.2, slower: 0.5
```

### Add Custom Animations:
1. Add your CSS selectors to the appropriate function
2. Use GSAP syntax for custom effects
3. Include ScrollTrigger for scroll-based animations

## Browser Support

- Modern browsers with GSAP 3.12.2 support
- Fallback: CSS transitions for older browsers
- Mobile optimized with touch event handling

## Files Modified

1. **functions.php** - Added GSAP scripts and animation styles
2. **assets/js/animations.js** - Main animation logic
3. **Inline CSS** - Animation enhancement styles

## Troubleshooting

### Animations Not Working:
1. Check browser console for JavaScript errors
2. Ensure GSAP is loading (check Network tab)
3. Verify elements exist when animations initialize

### Performance Issues:
1. Reduce animation duration
2. Limit number of animated elements
3. Use `will-change` sparingly

### Conflicts with Other Plugins:
1. Check for jQuery conflicts
2. Ensure GSAP loads before other animation libraries
3. Use WordPress script dependencies properly

## Future Enhancements

Potential additions:
- Page transition animations
- Parallax scrolling effects
- Advanced property showcase animations
- Interactive map animations
- Form field animations
