/**
 * Realome Theme GSAP Animations
 *
 * This file contains GSAP button animations with liquid effects for the Realome theme.
 */

// Global variables for liquid effect
let mouseX = 0, mouseY = 0;
let mouseLastX = 0, mouseLastY = 0, mouseDirectionX = 0, mouseDirectionY = 0;
let mouseSpeedX = 0, mouseSpeedY = 0;

// Configuration
const LIQUID_EFFECT_ENABLED = false; // Set to false to disable liquid effects

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {

    // Initialize mouse tracking
    initMouseTracking();

    // Initialize button animations
    initButtonAnimations();

    // Initialize navigation animations
    initNavigationAnimations();

});

/**
 * Mouse tracking for liquid effects
 */
function initMouseTracking() {
    // Track mouse movement
    document.addEventListener('mousemove', function(e) {
        // Get mouse direction
        if (mouseX < e.pageX) mouseDirectionX = 1;
        else if (mouseX > e.pageX) mouseDirectionX = -1;
        else mouseDirectionX = 0;

        if (mouseY < e.pageY) mouseDirectionY = 1;
        else if (mouseY > e.pageY) mouseDirectionY = -1;
        else mouseDirectionY = 0;

        mouseX = e.pageX;
        mouseY = e.pageY;
    });

    // Track mouse speed
    function updateMouseSpeed() {
        mouseSpeedX = mouseX - mouseLastX;
        mouseSpeedY = mouseY - mouseLastY;
        mouseLastX = mouseX;
        mouseLastY = mouseY;
        setTimeout(updateMouseSpeed, 50);
    }
    updateMouseSpeed();
}

/**
 * Button Animations with Liquid Effects
 * Enhanced hover effects for all buttons
 */
function initButtonAnimations() {

    // WordPress core buttons and theme buttons
    const buttons = document.querySelectorAll(`
        .wp-block-button__link,
        .wp-element-button,
        button,
        input[type="submit"],
        input[type="button"],
        .btn,
        a.button
    `);

    buttons.forEach(button => {
        // Add liquid effect to button if enabled
        if (LIQUID_EFFECT_ENABLED) {
            addLiquidEffect(button);
        }

        // Set initial state
        gsap.set(button, {
            scale: 1,
            boxShadow: "0 4px 15px rgba(0, 0, 0, 0.1)"
        });

        // Hover in animation
        button.addEventListener('mouseenter', () => {
            gsap.to(button, {
                scale: 1.05,
                boxShadow: "0 8px 25px rgba(0, 0, 0, 0.15)",
                duration: 0.3,
                ease: "power2.out"
            });
        });

        // Hover out animation
        button.addEventListener('mouseleave', () => {
            gsap.to(button, {
                scale: 1,
                boxShadow: "0 4px 15px rgba(0, 0, 0, 0.1)",
                duration: 0.3,
                ease: "power2.out"
            });
        });

        // Click animation
        button.addEventListener('mousedown', () => {
            gsap.to(button, {
                scale: 0.95,
                duration: 0.1,
                ease: "power2.out"
            });
        });

        button.addEventListener('mouseup', () => {
            gsap.to(button, {
                scale: 1.05,
                duration: 0.1,
                ease: "power2.out"
            });
        });
    });
}

/**
 * Navigation Animations
 * Drawing underline effects for navigation links
 */
function initNavigationAnimations() {

    // Target navigation links
    const navLinks = document.querySelectorAll(`
        .wp-block-navigation-item a,
        .wp-block-navigation-link a,
        nav a,
        .navigation a,
        .menu a
    `);

    navLinks.forEach(link => {
        // Skip if already processed or is a button
        if (link.classList.contains('nav-processed') ||
            link.classList.contains('wp-block-button__link') ||
            link.classList.contains('wp-element-button')) {
            return;
        }

        // Mark as processed
        link.classList.add('nav-processed');

        // Create underline element
        const underline = document.createElement('span');
        underline.className = 'nav-underline';

        // Make link relative positioned
        if (getComputedStyle(link).position === 'static') {
            link.style.position = 'relative';
        }

        // Add underline to link
        link.appendChild(underline);

        // Set initial underline state
        gsap.set(underline, {
            scaleX: 0,
            transformOrigin: 'left center'
        });

        // Check if link is currently active
        // For WordPress navigation block, check parent container classes
        const parentItem = link.closest('.wp-block-navigation-item, .wp-block-navigation-link');
        const isActive = link.classList.contains('current-menu-item') ||
                        link.classList.contains('current_page_item') ||
                        link.classList.contains('current-page-ancestor') ||
                        link.getAttribute('aria-current') === 'page' ||
                        (parentItem && (
                            parentItem.classList.contains('current-menu-item') ||
                            parentItem.classList.contains('current_page_item') ||
                            parentItem.classList.contains('current-page-ancestor') ||
                            parentItem.classList.contains('wp-block-navigation-item--current')
                        )) ||
                        window.location.href === link.href;

        if (isActive) {
            // Show underline for active state
            gsap.set(underline, { scaleX: 1 });
            link.classList.add('nav-active');
        }

        // Hover animations
        link.addEventListener('mouseenter', () => {
            if (!isActive) {
                gsap.to(underline, {
                    scaleX: 1,
                    duration: 0.3,
                    ease: 'power2.out'
                });
            }
        });

        link.addEventListener('mouseleave', () => {
            if (!isActive) {
                gsap.to(underline, {
                    scaleX: 0,
                    duration: 0.3,
                    ease: 'power2.out'
                });
            }
        });
    });
}

/**
 * Re-check navigation active states (for dynamic content)
 */
function recheckNavigationActiveStates() {
    const navLinks = document.querySelectorAll('.nav-processed');

    navLinks.forEach(link => {
        const underline = link.querySelector('.nav-underline');
        if (!underline) return;

        const parentItem = link.closest('.wp-block-navigation-item, .wp-block-navigation-link');
        const isActive = link.classList.contains('current-menu-item') ||
                        link.classList.contains('current_page_item') ||
                        link.classList.contains('current-page-ancestor') ||
                        link.getAttribute('aria-current') === 'page' ||
                        (parentItem && (
                            parentItem.classList.contains('current-menu-item') ||
                            parentItem.classList.contains('current_page_item') ||
                            parentItem.classList.contains('current-page-ancestor') ||
                            parentItem.classList.contains('wp-block-navigation-item--current')
                        )) ||
                        window.location.href === link.href;

        if (isActive) {
            gsap.set(underline, { scaleX: 1 });
            link.classList.add('nav-active');
        } else {
            link.classList.remove('nav-active');
            gsap.set(underline, { scaleX: 0 });
        }
    });
}

// Re-check active states after a short delay (for dynamic loading)
setTimeout(recheckNavigationActiveStates, 500);

/**
 * Add liquid effect to button
 */
function addLiquidEffect(button) {
    // Skip if button already has liquid effect
    if (button.querySelector('.liquid-canvas')) return;

    // Get button dimensions
    const buttonWidth = button.offsetWidth;
    const buttonHeight = button.offsetHeight;

    // Skip very small buttons
    if (buttonWidth < 80 || buttonHeight < 30) return;

    // Create canvas
    const canvas = document.createElement('canvas');
    canvas.className = 'liquid-canvas';
    canvas.width = buttonWidth + 20;
    canvas.height = buttonHeight + 20;
    canvas.style.position = 'absolute';
    canvas.style.top = '-10px';
    canvas.style.left = '-10px';
    canvas.style.zIndex = '1';
    canvas.style.pointerEvents = 'none';
    canvas.style.borderRadius = '8px';

    // Make button relative positioned
    if (getComputedStyle(button).position === 'static') {
        button.style.position = 'relative';
    }

    // Ensure button content is above canvas
    const buttonContent = button.innerHTML;
    button.innerHTML = `<span class="button-content" style="position: relative; z-index: 2;">${buttonContent}</span>`;

    // Add canvas to button
    button.appendChild(canvas);

    // Initialize simplified liquid animation
    initSimpleLiquidAnimation(canvas, button, buttonWidth, buttonHeight);
}

/**
 * Initialize simplified liquid animation for a button
 */
function initSimpleLiquidAnimation(canvas, button, buttonWidth, buttonHeight) {
    const context = canvas.getContext('2d');
    let animationId;
    let time = 0;

    // Render function with simple wave effect
    function render() {
        time += 0.05;

        // Clear canvas
        context.clearRect(0, 0, canvas.width, canvas.height);

        // Get mouse position relative to button
        const buttonRect = button.getBoundingClientRect();
        const relMouseX = mouseX - buttonRect.left;
        const relMouseY = mouseY - buttonRect.top;

        // Create animated gradient
        const gradient = context.createRadialGradient(
            relMouseX + 10, relMouseY + 10, 0,
            relMouseX + 10, relMouseY + 10, buttonWidth
        );

        // Use theme colors with animation
        const hue1 = (time * 50) % 360;
        const hue2 = (time * 50 + 60) % 360;

        gradient.addColorStop(0, `hsl(${hue1}, 70%, 60%)`);
        gradient.addColorStop(0.5, `hsl(${hue2}, 70%, 50%)`);
        gradient.addColorStop(1, `hsl(${hue1 + 30}, 70%, 40%)`);

        // Draw liquid shape with wave effect
        context.fillStyle = gradient;
        context.beginPath();

        // Create wavy border
        const waveHeight = 3;
        const waveFreq = 0.02;

        // Top edge with wave
        context.moveTo(8, 10);
        for (let x = 8; x < buttonWidth + 12; x += 2) {
            const y = 10 + Math.sin(x * waveFreq + time) * waveHeight;
            context.lineTo(x, y);
        }

        // Right edge
        for (let y = 10; y < buttonHeight + 10; y += 2) {
            const x = buttonWidth + 12 + Math.sin(y * waveFreq + time + 1.5) * waveHeight;
            context.lineTo(x, y);
        }

        // Bottom edge with wave
        for (let x = buttonWidth + 12; x > 8; x -= 2) {
            const y = buttonHeight + 10 + Math.sin(x * waveFreq + time + 3) * waveHeight;
            context.lineTo(x, y);
        }

        // Left edge
        for (let y = buttonHeight + 10; y > 10; y -= 2) {
            const x = 8 + Math.sin(y * waveFreq + time + 4.5) * waveHeight;
            context.lineTo(x, y);
        }

        context.closePath();
        context.fill();

        animationId = requestAnimationFrame(render);
    }

    // Start animation on hover
    button.addEventListener('mouseenter', () => {
        render();
    });

    // Stop animation on leave
    button.addEventListener('mouseleave', () => {
        if (animationId) {
            cancelAnimationFrame(animationId);
        }
        // Clear canvas
        context.clearRect(0, 0, canvas.width, canvas.height);
    });
}








