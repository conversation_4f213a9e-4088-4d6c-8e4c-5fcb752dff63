/**
 * Realome Theme GSAP Animations
 * 
 * This file contains all GSAP animations for the Realome theme
 * including button hover effects, scroll animations, and page transitions.
 */

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {

    // Register ScrollTrigger plugin
    gsap.registerPlugin(ScrollTrigger);

    // Mark site as loaded for CSS
    const siteBlocks = document.querySelector('.wp-site-blocks');
    if (siteBlocks) {
        siteBlocks.classList.add('loaded');
    }

    // Initialize all animations
    initButtonAnimations();
    initScrollAnimations();
    initPageLoadAnimations();
    initHoverAnimations();

});

/**
 * Button Animations
 * Smooth hover effects for all buttons
 */
function initButtonAnimations() {
    
    // WordPress core buttons and theme buttons
    const buttons = document.querySelectorAll(`
        .wp-block-button__link,
        .wp-element-button,
        button,
        input[type="submit"],
        input[type="button"],
        .btn,
        a.button
    `);
    
    buttons.forEach(button => {
        // Set initial state
        gsap.set(button, {
            scale: 1,
            boxShadow: "0 4px 15px rgba(0, 0, 0, 0.1)"
        });
        
        // Hover in animation
        button.addEventListener('mouseenter', () => {
            gsap.to(button, {
                scale: 1.05,
                boxShadow: "0 8px 25px rgba(0, 0, 0, 0.15)",
                duration: 0.3,
                ease: "power2.out"
            });
        });
        
        // Hover out animation
        button.addEventListener('mouseleave', () => {
            gsap.to(button, {
                scale: 1,
                boxShadow: "0 4px 15px rgba(0, 0, 0, 0.1)",
                duration: 0.3,
                ease: "power2.out"
            });
        });
        
        // Click animation
        button.addEventListener('mousedown', () => {
            gsap.to(button, {
                scale: 0.95,
                duration: 0.1,
                ease: "power2.out"
            });
        });
        
        button.addEventListener('mouseup', () => {
            gsap.to(button, {
                scale: 1.05,
                duration: 0.1,
                ease: "power2.out"
            });
        });
    });
}

/**
 * Scroll-triggered animations
 * Elements animate in as they come into view
 */
function initScrollAnimations() {
    
    // Fade in from bottom animation
    const fadeUpElements = document.querySelectorAll(`
        .wp-block-heading,
        .wp-block-paragraph,
        .wp-block-image,
        .wp-block-group,
        .wp-block-columns,
        .wp-block-media-text,
        .wp-block-cover
    `);
    
    fadeUpElements.forEach(element => {
        gsap.fromTo(element, 
            {
                opacity: 0,
                y: 50
            },
            {
                opacity: 1,
                y: 0,
                duration: 0.8,
                ease: "power2.out",
                scrollTrigger: {
                    trigger: element,
                    start: "top 85%",
                    end: "bottom 15%",
                    toggleActions: "play none none reverse"
                }
            }
        );
    });
    
    // Scale in animation for images
    const images = document.querySelectorAll('.wp-block-image img, .wp-block-media-text img');
    images.forEach(img => {
        gsap.fromTo(img,
            {
                scale: 0.8,
                opacity: 0
            },
            {
                scale: 1,
                opacity: 1,
                duration: 1,
                ease: "power2.out",
                scrollTrigger: {
                    trigger: img,
                    start: "top 85%",
                    toggleActions: "play none none reverse"
                }
            }
        );
    });
    
    // Stagger animation for columns
    const columnContainers = document.querySelectorAll('.wp-block-columns');
    columnContainers.forEach(container => {
        const columns = container.querySelectorAll('.wp-block-column');
        if (columns.length > 1) {
            gsap.fromTo(columns,
                {
                    opacity: 0,
                    y: 30
                },
                {
                    opacity: 1,
                    y: 0,
                    duration: 0.6,
                    stagger: 0.2,
                    ease: "power2.out",
                    scrollTrigger: {
                        trigger: container,
                        start: "top 85%",
                        toggleActions: "play none none reverse"
                    }
                }
            );
        }
    });
}

/**
 * Page load animations
 * Initial animations when page loads
 */
function initPageLoadAnimations() {
    
    // Animate header/navigation
    const header = document.querySelector('header, .wp-block-template-part[data-area="header"]');
    if (header) {
        gsap.fromTo(header,
            {
                opacity: 0,
                y: -50
            },
            {
                opacity: 1,
                y: 0,
                duration: 0.8,
                ease: "power2.out",
                delay: 0.2
            }
        );
    }
    
    // Animate main content area
    const main = document.querySelector('main, .wp-site-blocks');
    if (main) {
        gsap.fromTo(main,
            {
                opacity: 0
            },
            {
                opacity: 1,
                duration: 1,
                ease: "power2.out",
                delay: 0.4
            }
        );
    }
    
    // Hero section special animation
    const heroSection = document.querySelector('.wp-block-cover, .hero-section, [class*="hero"]');
    if (heroSection) {
        const heroContent = heroSection.querySelectorAll('h1, h2, p, .wp-block-button');
        gsap.fromTo(heroContent,
            {
                opacity: 0,
                y: 30
            },
            {
                opacity: 1,
                y: 0,
                duration: 0.8,
                stagger: 0.2,
                ease: "power2.out",
                delay: 0.6
            }
        );
    }
}

/**
 * Hover animations for various elements
 */
function initHoverAnimations() {
    
    // Card hover effects
    const cards = document.querySelectorAll(`
        .wp-block-group,
        .wp-block-media-text,
        .post,
        .card,
        [class*="card"]
    `);
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            gsap.to(card, {
                y: -5,
                boxShadow: "0 10px 30px rgba(0, 0, 0, 0.1)",
                duration: 0.3,
                ease: "power2.out"
            });
        });
        
        card.addEventListener('mouseleave', () => {
            gsap.to(card, {
                y: 0,
                boxShadow: "0 2px 10px rgba(0, 0, 0, 0.05)",
                duration: 0.3,
                ease: "power2.out"
            });
        });
    });
    
    // Link hover effects
    const links = document.querySelectorAll('a:not(.wp-block-button__link):not(.wp-element-button)');
    links.forEach(link => {
        link.addEventListener('mouseenter', () => {
            gsap.to(link, {
                scale: 1.02,
                duration: 0.2,
                ease: "power2.out"
            });
        });
        
        link.addEventListener('mouseleave', () => {
            gsap.to(link, {
                scale: 1,
                duration: 0.2,
                ease: "power2.out"
            });
        });
    });
    
    // Navigation menu animations
    const navItems = document.querySelectorAll('.wp-block-navigation-item a');
    navItems.forEach(item => {
        item.addEventListener('mouseenter', () => {
            gsap.to(item, {
                color: "var(--wp--preset--color--primary, #63D070)",
                duration: 0.2,
                ease: "power2.out"
            });
        });
        
        item.addEventListener('mouseleave', () => {
            gsap.to(item, {
                color: "inherit",
                duration: 0.2,
                ease: "power2.out"
            });
        });
    });
}

/**
 * Utility function to add smooth scrolling
 */
function initSmoothScrolling() {
    // Smooth scroll for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const target = document.querySelector(link.getAttribute('href'));
            if (target) {
                gsap.to(window, {
                    duration: 1,
                    scrollTo: target,
                    ease: "power2.inOut"
                });
            }
        });
    });
}

// Initialize smooth scrolling
initSmoothScrolling();

/**
 * Real Estate specific animations
 * Property cards, listings, etc.
 */
function initRealEstateAnimations() {

    // Property card animations
    const propertyCards = document.querySelectorAll(`
        .property-card,
        .listing-card,
        [class*="listing"],
        [class*="property"]
    `);

    propertyCards.forEach(card => {
        // Enhanced hover effect for property cards
        card.addEventListener('mouseenter', () => {
            gsap.to(card, {
                y: -8,
                scale: 1.02,
                boxShadow: "0 15px 40px rgba(0, 0, 0, 0.15)",
                duration: 0.4,
                ease: "power2.out"
            });

            // Animate property image if exists
            const img = card.querySelector('img');
            if (img) {
                gsap.to(img, {
                    scale: 1.05,
                    duration: 0.4,
                    ease: "power2.out"
                });
            }
        });

        card.addEventListener('mouseleave', () => {
            gsap.to(card, {
                y: 0,
                scale: 1,
                boxShadow: "0 5px 20px rgba(0, 0, 0, 0.08)",
                duration: 0.4,
                ease: "power2.out"
            });

            const img = card.querySelector('img');
            if (img) {
                gsap.to(img, {
                    scale: 1,
                    duration: 0.4,
                    ease: "power2.out"
                });
            }
        });
    });

    // Price animation
    const priceElements = document.querySelectorAll(`
        .price,
        .property-price,
        [class*="price"]
    `);

    priceElements.forEach(price => {
        gsap.fromTo(price,
            {
                opacity: 0,
                scale: 0.8
            },
            {
                opacity: 1,
                scale: 1,
                duration: 0.6,
                ease: "back.out(1.7)",
                scrollTrigger: {
                    trigger: price,
                    start: "top 90%",
                    toggleActions: "play none none reverse"
                }
            }
        );
    });
}

// Initialize real estate animations
initRealEstateAnimations();

/**
 * Utility functions for custom animations
 */
window.RealomeAnimations = {

    // Animate element in from left
    slideInLeft: function(element, delay = 0) {
        gsap.fromTo(element,
            { x: -100, opacity: 0 },
            { x: 0, opacity: 1, duration: 0.8, delay: delay, ease: "power2.out" }
        );
    },

    // Animate element in from right
    slideInRight: function(element, delay = 0) {
        gsap.fromTo(element,
            { x: 100, opacity: 0 },
            { x: 0, opacity: 1, duration: 0.8, delay: delay, ease: "power2.out" }
        );
    },

    // Bounce in animation
    bounceIn: function(element, delay = 0) {
        gsap.fromTo(element,
            { scale: 0, opacity: 0 },
            { scale: 1, opacity: 1, duration: 0.6, delay: delay, ease: "back.out(1.7)" }
        );
    },

    // Fade in animation
    fadeIn: function(element, delay = 0) {
        gsap.fromTo(element,
            { opacity: 0 },
            { opacity: 1, duration: 0.8, delay: delay, ease: "power2.out" }
        );
    },

    // Stagger animation for multiple elements
    staggerIn: function(elements, delay = 0) {
        gsap.fromTo(elements,
            { y: 30, opacity: 0 },
            { y: 0, opacity: 1, duration: 0.6, delay: delay, stagger: 0.1, ease: "power2.out" }
        );
    }
};

/**
 * Add custom CSS classes for animation triggers
 */
function addAnimationClasses() {
    // Add animation classes to common elements
    const animateElements = document.querySelectorAll(`
        .wp-block-heading:not(.animated),
        .wp-block-paragraph:not(.animated),
        .wp-block-image:not(.animated)
    `);

    animateElements.forEach(element => {
        element.classList.add('animate-on-scroll');
    });
}

// Add animation classes after DOM is ready
addAnimationClasses();

/**
 * Refresh ScrollTrigger on window resize
 */
window.addEventListener('resize', () => {
    ScrollTrigger.refresh();
});
