/**
 * Realome Theme GSAP Animations
 *
 * This file contains GSAP button animations for the Realome theme.
 */

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {

    // Initialize button animations only
    initButtonAnimations();

});

/**
 * Button Animations
 * Smooth hover effects for all buttons
 */
function initButtonAnimations() {
    
    // WordPress core buttons and theme buttons
    const buttons = document.querySelectorAll(`
        .wp-block-button__link,
        .wp-element-button,
        button,
        input[type="submit"],
        input[type="button"],
        .btn,
        a.button
    `);
    
    buttons.forEach(button => {
        // Set initial state
        gsap.set(button, {
            scale: 1,
            boxShadow: "0 4px 15px rgba(0, 0, 0, 0.1)"
        });
        
        // Hover in animation
        button.addEventListener('mouseenter', () => {
            gsap.to(button, {
                scale: 1.05,
                boxShadow: "0 8px 25px rgba(0, 0, 0, 0.15)",
                duration: 0.3,
                ease: "power2.out"
            });
        });
        
        // Hover out animation
        button.addEventListener('mouseleave', () => {
            gsap.to(button, {
                scale: 1,
                boxShadow: "0 4px 15px rgba(0, 0, 0, 0.1)",
                duration: 0.3,
                ease: "power2.out"
            });
        });
        
        // Click animation
        button.addEventListener('mousedown', () => {
            gsap.to(button, {
                scale: 0.95,
                duration: 0.1,
                ease: "power2.out"
            });
        });
        
        button.addEventListener('mouseup', () => {
            gsap.to(button, {
                scale: 1.05,
                duration: 0.1,
                ease: "power2.out"
            });
        });
    });
}








