/**
 * Realome Cursor Tracking Effects
 * 
 * Makes homepage images follow cursor movement with smooth GSAP animations
 */

// Configuration
const CURSOR_TRACKING_CONFIG = {
    enabled: true,
    strength: 0.1,        // How much images move (0.1 = 10% of cursor movement)
    smoothness: 0.15,     // Animation smoothness (lower = smoother)
    maxMove: 30,          // Maximum pixels images can move
    onlyHomepage: true,   // Only activate on homepage
    selector: '.cursor-track-image' // Class to add to images you want to track
};

// Global variables
let mouseX = 0;
let mouseY = 0;
let windowCenterX = 0;
let windowCenterY = 0;

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    if (CURSOR_TRACKING_CONFIG.enabled && typeof gsap !== 'undefined') {
        initCursorTracking();
    }
});

/**
 * Initialize cursor tracking for homepage images
 */
function initCursorTracking() {
    
    // Check if we should only run on homepage
    if (CURSOR_TRACKING_CONFIG.onlyHomepage && !isHomepage()) {
        return;
    }
    
    // Update window center on load and resize
    updateWindowCenter();
    window.addEventListener('resize', updateWindowCenter);
    
    // Track mouse movement
    document.addEventListener('mousemove', updateMousePosition);
    
    // Find and setup trackable images
    setupTrackableImages();
    
    // Auto-detect homepage hero images if no manual class is found
    if (document.querySelectorAll(CURSOR_TRACKING_CONFIG.selector).length === 0) {
        autoDetectHeroImages();
    }
}

/**
 * Check if current page is homepage
 */
function isHomepage() {
    return document.body.classList.contains('home') || 
           document.body.classList.contains('front-page') ||
           window.location.pathname === '/' ||
           window.location.pathname === '/index.php';
}

/**
 * Update window center coordinates
 */
function updateWindowCenter() {
    windowCenterX = window.innerWidth / 2;
    windowCenterY = window.innerHeight / 2;
}

/**
 * Update mouse position
 */
function updateMousePosition(e) {
    mouseX = e.clientX;
    mouseY = e.clientY;
}

/**
 * Setup images with cursor tracking class
 */
function setupTrackableImages() {
    const trackableImages = document.querySelectorAll(CURSOR_TRACKING_CONFIG.selector);
    
    trackableImages.forEach(image => {
        setupImageTracking(image);
    });
}

/**
 * Auto-detect hero/featured images on homepage
 */
function autoDetectHeroImages() {
    const heroSelectors = [
        '.wp-block-cover img',
        '.wp-block-media-text img',
        '.hero img',
        '.hero-section img',
        '.wp-block-image img',
        '.featured-image img',
        '.property-showcase img:first-child'
    ];
    
    heroSelectors.forEach(selector => {
        const images = document.querySelectorAll(selector);
        images.forEach((image, index) => {
            // Only track first few images to avoid performance issues
            if (index < 3) {
                image.classList.add('cursor-track-image');
                setupImageTracking(image);
            }
        });
    });
}

/**
 * Setup cursor tracking for a specific image
 */
function setupImageTracking(image) {
    // Skip if already setup
    if (image.hasAttribute('data-cursor-tracking')) {
        return;
    }
    
    // Mark as setup
    image.setAttribute('data-cursor-tracking', 'true');
    
    // Set initial transform origin to center
    gsap.set(image, {
        transformOrigin: 'center center',
        willChange: 'transform'
    });
    
    // Start the tracking animation
    startImageTracking(image);
}

/**
 * Start cursor tracking animation for an image
 */
function startImageTracking(image) {
    
    function updateImagePosition() {
        // Calculate distance from center
        const deltaX = mouseX - windowCenterX;
        const deltaY = mouseY - windowCenterY;
        
        // Apply strength and limit movement
        let moveX = deltaX * CURSOR_TRACKING_CONFIG.strength;
        let moveY = deltaY * CURSOR_TRACKING_CONFIG.strength;
        
        // Limit maximum movement
        moveX = Math.max(-CURSOR_TRACKING_CONFIG.maxMove, Math.min(CURSOR_TRACKING_CONFIG.maxMove, moveX));
        moveY = Math.max(-CURSOR_TRACKING_CONFIG.maxMove, Math.min(CURSOR_TRACKING_CONFIG.maxMove, moveY));
        
        // Apply smooth animation
        gsap.to(image, {
            x: moveX,
            y: moveY,
            duration: CURSOR_TRACKING_CONFIG.smoothness,
            ease: 'power2.out'
        });
        
        // Continue animation
        requestAnimationFrame(updateImagePosition);
    }
    
    // Start the animation loop
    updateImagePosition();
}

/**
 * Add cursor tracking to specific images manually
 */
window.RealomeCursorTracking = {
    
    // Add tracking to specific element
    addTracking: function(element) {
        if (element && typeof gsap !== 'undefined') {
            element.classList.add('cursor-track-image');
            setupImageTracking(element);
        }
    },
    
    // Remove tracking from element
    removeTracking: function(element) {
        if (element) {
            element.classList.remove('cursor-track-image');
            element.removeAttribute('data-cursor-tracking');
            gsap.set(element, { x: 0, y: 0, clearProps: 'transform' });
        }
    },
    
    // Update configuration
    updateConfig: function(newConfig) {
        Object.assign(CURSOR_TRACKING_CONFIG, newConfig);
    },
    
    // Enable/disable tracking
    toggle: function(enabled) {
        CURSOR_TRACKING_CONFIG.enabled = enabled;
        if (!enabled) {
            // Reset all tracked images
            const trackedImages = document.querySelectorAll('[data-cursor-tracking]');
            trackedImages.forEach(image => {
                gsap.set(image, { x: 0, y: 0, clearProps: 'transform' });
            });
        }
    }
};

/**
 * Enhanced cursor tracking with parallax effect
 */
function initParallaxCursorTracking() {
    const parallaxImages = document.querySelectorAll('.parallax-cursor-track');
    
    parallaxImages.forEach((image, index) => {
        const depth = (index + 1) * 0.05; // Different depths for layered effect
        
        function updateParallaxPosition() {
            const deltaX = (mouseX - windowCenterX) * depth;
            const deltaY = (mouseY - windowCenterY) * depth;
            
            gsap.to(image, {
                x: deltaX,
                y: deltaY,
                duration: 0.3,
                ease: 'power2.out'
            });
            
            requestAnimationFrame(updateParallaxPosition);
        }
        
        updateParallaxPosition();
    });
}

/**
 * Magnetic cursor effect for special elements
 */
function initMagneticCursorEffect() {
    const magneticElements = document.querySelectorAll('.magnetic-cursor');
    
    magneticElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            const rect = element.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            
            function magneticUpdate() {
                const deltaX = (mouseX - centerX) * 0.3;
                const deltaY = (mouseY - centerY) * 0.3;
                
                gsap.to(element, {
                    x: deltaX,
                    y: deltaY,
                    duration: 0.3,
                    ease: 'power2.out'
                });
            }
            
            const magneticInterval = setInterval(magneticUpdate, 16);
            
            element.addEventListener('mouseleave', function() {
                clearInterval(magneticInterval);
                gsap.to(element, {
                    x: 0,
                    y: 0,
                    duration: 0.5,
                    ease: 'elastic.out(1, 0.3)'
                });
            }, { once: true });
        });
    });
}

// Initialize additional effects
document.addEventListener('DOMContentLoaded', function() {
    if (typeof gsap !== 'undefined') {
        setTimeout(() => {
            initParallaxCursorTracking();
            initMagneticCursorEffect();
        }, 1000);
    }
});

// Performance optimization: pause animations when page is not visible
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // Pause animations when tab is not active
        gsap.globalTimeline.pause();
    } else {
        // Resume animations when tab becomes active
        gsap.globalTimeline.resume();
    }
});
