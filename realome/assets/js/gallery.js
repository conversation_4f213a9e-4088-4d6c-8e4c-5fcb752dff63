/**
 * Realome Gallery & Lightbox
 * 
 * Enhanced gallery functionality with lightbox for property images
 */

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    initGalleryLightbox();
    enhanceGalleryStyles();
});

/**
 * Initialize lightbox functionality for galleries and images
 */
function initGalleryLightbox() {
    
    // Target gallery images and standalone images
    const galleryImages = document.querySelectorAll(`
        .wp-block-gallery img,
        .wp-block-image img,
        .gallery img,
        .property-gallery img,
        .listing-gallery img
    `);
    
    galleryImages.forEach((img, index) => {
        // Skip if image is very small (likely icons)
        if (img.width < 100 || img.height < 100) return;
        
        // Make image clickable
        img.style.cursor = 'pointer';
        img.setAttribute('data-lightbox-index', index);
        
        // Add click event
        img.addEventListener('click', function(e) {
            e.preventDefault();
            openLightbox(img, index, galleryImages);
        });
        
        // Add hover effect
        img.addEventListener('mouseenter', function() {
            img.style.transform = 'scale(1.02)';
            img.style.transition = 'transform 0.3s ease';
        });
        
        img.addEventListener('mouseleave', function() {
            img.style.transform = 'scale(1)';
        });
    });
}

/**
 * Open lightbox with image
 */
function openLightbox(clickedImg, currentIndex, allImages) {
    
    // Create lightbox overlay
    const lightbox = document.createElement('div');
    lightbox.className = 'realome-lightbox';
    lightbox.innerHTML = `
        <div class="lightbox-overlay">
            <div class="lightbox-container">
                <button class="lightbox-close" aria-label="Close">&times;</button>
                <button class="lightbox-prev" aria-label="Previous">&#8249;</button>
                <button class="lightbox-next" aria-label="Next">&#8250;</button>
                <div class="lightbox-content">
                    <img src="${clickedImg.src}" alt="${clickedImg.alt || 'Property Image'}" class="lightbox-image">
                    <div class="lightbox-info">
                        <span class="lightbox-counter">${currentIndex + 1} / ${allImages.length}</span>
                        <p class="lightbox-caption">${clickedImg.alt || 'Property Image'}</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Add to body
    document.body.appendChild(lightbox);
    document.body.style.overflow = 'hidden';
    
    // Get elements
    const overlay = lightbox.querySelector('.lightbox-overlay');
    const closeBtn = lightbox.querySelector('.lightbox-close');
    const prevBtn = lightbox.querySelector('.lightbox-prev');
    const nextBtn = lightbox.querySelector('.lightbox-next');
    const lightboxImg = lightbox.querySelector('.lightbox-image');
    const counter = lightbox.querySelector('.lightbox-counter');
    const caption = lightbox.querySelector('.lightbox-caption');
    
    let current = currentIndex;
    
    // Update image function
    function updateImage(index) {
        const img = allImages[index];
        lightboxImg.src = img.src;
        lightboxImg.alt = img.alt || 'Property Image';
        counter.textContent = `${index + 1} / ${allImages.length}`;
        caption.textContent = img.alt || 'Property Image';
        current = index;
    }
    
    // Navigation functions
    function showNext() {
        const next = (current + 1) % allImages.length;
        updateImage(next);
    }
    
    function showPrev() {
        const prev = (current - 1 + allImages.length) % allImages.length;
        updateImage(prev);
    }
    
    // Close function
    function closeLightbox() {
        document.body.removeChild(lightbox);
        document.body.style.overflow = '';
    }
    
    // Event listeners
    closeBtn.addEventListener('click', closeLightbox);
    overlay.addEventListener('click', function(e) {
        if (e.target === overlay) closeLightbox();
    });
    
    // Navigation
    if (allImages.length > 1) {
        nextBtn.addEventListener('click', showNext);
        prevBtn.addEventListener('click', showPrev);
    } else {
        nextBtn.style.display = 'none';
        prevBtn.style.display = 'none';
    }
    
    // Keyboard navigation
    function handleKeyboard(e) {
        switch(e.key) {
            case 'Escape':
                closeLightbox();
                break;
            case 'ArrowRight':
                if (allImages.length > 1) showNext();
                break;
            case 'ArrowLeft':
                if (allImages.length > 1) showPrev();
                break;
        }
    }
    
    document.addEventListener('keydown', handleKeyboard);
    
    // Remove keyboard listener when closed
    const originalClose = closeLightbox;
    closeLightbox = function() {
        document.removeEventListener('keydown', handleKeyboard);
        originalClose();
    };
    
    // Animate in
    setTimeout(() => {
        lightbox.classList.add('active');
    }, 10);
}

/**
 * Enhance gallery styles
 */
function enhanceGalleryStyles() {
    
    // Add enhanced styling to galleries
    const galleries = document.querySelectorAll('.wp-block-gallery');
    
    galleries.forEach(gallery => {
        gallery.classList.add('realome-enhanced-gallery');
        
        // Add property gallery class if it looks like property images
        const images = gallery.querySelectorAll('img');
        if (images.length >= 3) {
            gallery.classList.add('property-showcase');
        }
    });
    
    // Enhance individual images
    const standaloneImages = document.querySelectorAll('.wp-block-image');
    standaloneImages.forEach(imageBlock => {
        const img = imageBlock.querySelector('img');
        if (img && img.width > 200) {
            imageBlock.classList.add('realome-enhanced-image');
        }
    });
}

/**
 * Add gallery grid layout for property showcases
 */
function initPropertyGalleryLayout() {
    
    const propertyGalleries = document.querySelectorAll('.property-showcase');
    
    propertyGalleries.forEach(gallery => {
        const images = gallery.querySelectorAll('img');
        
        if (images.length >= 4) {
            gallery.classList.add('property-grid-layout');
            
            // Add special classes for featured layout
            images[0]?.parentElement.classList.add('featured-image');
            
            for (let i = 1; i < Math.min(images.length, 5); i++) {
                images[i]?.parentElement.classList.add('grid-image');
            }
            
            // Add "more images" overlay if there are more than 5 images
            if (images.length > 5) {
                const lastVisible = images[4].parentElement;
                const overlay = document.createElement('div');
                overlay.className = 'more-images-overlay';
                overlay.innerHTML = `<span>+${images.length - 5} more</span>`;
                lastVisible.appendChild(overlay);
            }
        }
    });
}

// Initialize property gallery layout
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initPropertyGalleryLayout, 100);
});

/**
 * Touch/swipe support for mobile
 */
function addTouchSupport(lightbox) {
    let startX = 0;
    let startY = 0;
    
    lightbox.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
    });
    
    lightbox.addEventListener('touchend', function(e) {
        if (!startX || !startY) return;
        
        const endX = e.changedTouches[0].clientX;
        const endY = e.changedTouches[0].clientY;
        
        const diffX = startX - endX;
        const diffY = startY - endY;
        
        // Horizontal swipe
        if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
            if (diffX > 0) {
                // Swipe left - next image
                const nextBtn = lightbox.querySelector('.lightbox-next');
                if (nextBtn) nextBtn.click();
            } else {
                // Swipe right - previous image
                const prevBtn = lightbox.querySelector('.lightbox-prev');
                if (prevBtn) prevBtn.click();
            }
        }
        
        startX = 0;
        startY = 0;
    });
}
