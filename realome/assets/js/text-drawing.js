/**
 * Realome Text Drawing Animation
 * 
 * Creates SVG text drawing effects for headings with .gsap-animation-heading class
 */

// Configuration
const TEXT_DRAWING_CONFIG = {
    colors: ["#63D070", "#EEC543", "#8763cf", "#FF6B6B", "#4ECDC4"],
    clones: 5,
    duration: 1.15,
    stagger: 0.25,
    nestedDelay: 0.25,
    letterDelay: 0.35,
    strokeWidth: 3,
    enabled: true
};

// Wait for DOM and GSAP to be ready
document.addEventListener('DOMContentLoaded', function() {
    if (typeof gsap !== 'undefined' && TEXT_DRAWING_CONFIG.enabled) {
        initTextDrawingAnimations();
    }
});

/**
 * Initialize text drawing animations for elements with .gsap-animation-heading
 */
function initTextDrawingAnimations() {
    
    // Configure GSAP
    if (typeof DrawSVGPlugin !== 'undefined') {
        gsap.registerPlugin(DrawSVGPlugin);
    }
    gsap.config({ trialWarn: false });
    
    // Find all elements with the animation class
    const animatedHeadings = document.querySelectorAll('.gsap-animation-heading');
    
    animatedHeadings.forEach((heading, index) => {
        // Skip if already processed
        if (heading.classList.contains('gsap-text-processed')) return;
        
        // Mark as processed
        heading.classList.add('gsap-text-processed');
        
        // Convert text to SVG with drawing animation
        convertTextToDrawingSVG(heading, index);
    });
}

/**
 * Convert text element to animated SVG
 */
function convertTextToDrawingSVG(element, index) {
    const text = element.textContent.trim();
    if (!text) return;
    
    // Get computed styles
    const styles = window.getComputedStyle(element);
    const fontSize = parseInt(styles.fontSize);
    const fontFamily = styles.fontFamily;
    const fontWeight = styles.fontWeight;
    
    // Create SVG container
    const svgContainer = document.createElement('div');
    svgContainer.className = 'gsap-text-drawing-container';
    
    // Create SVG with text path (simplified approach)
    const svgId = `text-drawing-${index}`;
    svgContainer.innerHTML = createTextSVG(text, svgId, fontSize, fontFamily, fontWeight);
    
    // Replace original element content
    element.innerHTML = '';
    element.appendChild(svgContainer);
    
    // Initialize animation with fallback for non-DrawSVG
    if (typeof DrawSVGPlugin !== 'undefined') {
        initDrawSVGAnimation(svgId);
    } else {
        initFallbackAnimation(svgId);
    }
}

/**
 * Create SVG markup for text
 */
function createTextSVG(text, svgId, fontSize, fontFamily, fontWeight) {
    const letters = text.split('');
    const letterWidth = fontSize * 0.6; // Approximate letter width
    const svgWidth = letters.length * letterWidth + 100;
    const svgHeight = fontSize * 1.5;
    
    let pathElements = '';
    
    // Create simplified letter paths (basic shapes for common letters)
    letters.forEach((letter, i) => {
        const x = i * letterWidth + 50;
        const y = svgHeight / 2;
        
        const letterPath = getLetterPath(letter.toUpperCase(), x, y, fontSize);
        if (letterPath) {
            pathElements += `<g class="letter-group" data-letter="${letter}">
                <path d="${letterPath}" class="letter-path"/>
            </g>`;
        }
    });
    
    return `
        <svg id="${svgId}" class="text-drawing-svg" 
             width="${svgWidth}" height="${svgHeight}" 
             viewBox="0 0 ${svgWidth} ${svgHeight}"
             style="max-width: 100%; height: auto;">
            ${pathElements}
            <text x="50" y="${svgHeight/2 + fontSize/3}" 
                  font-family="${fontFamily}" 
                  font-size="${fontSize}" 
                  font-weight="${fontWeight}"
                  fill="currentColor" 
                  class="fallback-text" 
                  style="opacity: 0;">
                ${text}
            </text>
        </svg>
    `;
}

/**
 * Get simplified SVG path for letters
 */
function getLetterPath(letter, x, y, size) {
    const paths = {
        'A': `M${x},${y+size/2} L${x+size/4},${y-size/2} L${x+size/2},${y+size/2} M${x+size/8},${y} L${x+size*3/8},${y}`,
        'B': `M${x},${y-size/2} L${x},${y+size/2} M${x},${y-size/2} L${x+size/3},${y-size/2} L${x+size/3},${y} L${x},${y} M${x},${y} L${x+size/3},${y} L${x+size/3},${y+size/2} L${x},${y+size/2}`,
        'C': `M${x+size/3},${y-size/2} L${x},${y-size/2} L${x},${y+size/2} L${x+size/3},${y+size/2}`,
        'D': `M${x},${y-size/2} L${x},${y+size/2} M${x},${y-size/2} L${x+size/4},${y-size/2} L${x+size/3},${y} L${x+size/4},${y+size/2} L${x},${y+size/2}`,
        'E': `M${x},${y-size/2} L${x},${y+size/2} M${x},${y-size/2} L${x+size/3},${y-size/2} M${x},${y} L${x+size/4},${y} M${x},${y+size/2} L${x+size/3},${y+size/2}`,
        'F': `M${x},${y-size/2} L${x},${y+size/2} M${x},${y-size/2} L${x+size/3},${y-size/2} M${x},${y} L${x+size/4},${y}`,
        'G': `M${x+size/3},${y-size/2} L${x},${y-size/2} L${x},${y+size/2} L${x+size/3},${y+size/2} L${x+size/3},${y} L${x+size/6},${y}`,
        'H': `M${x},${y-size/2} L${x},${y+size/2} M${x+size/3},${y-size/2} L${x+size/3},${y+size/2} M${x},${y} L${x+size/3},${y}`,
        'I': `M${x},${y-size/2} L${x+size/3},${y-size/2} M${x+size/6},${y-size/2} L${x+size/6},${y+size/2} M${x},${y+size/2} L${x+size/3},${y+size/2}`,
        'L': `M${x},${y-size/2} L${x},${y+size/2} L${x+size/3},${y+size/2}`,
        'M': `M${x},${y+size/2} L${x},${y-size/2} L${x+size/6},${y} L${x+size/3},${y-size/2} L${x+size/3},${y+size/2}`,
        'N': `M${x},${y+size/2} L${x},${y-size/2} L${x+size/3},${y+size/2} L${x+size/3},${y-size/2}`,
        'O': `M${x},${y-size/2} L${x+size/3},${y-size/2} L${x+size/3},${y+size/2} L${x},${y+size/2} L${x},${y-size/2}`,
        'P': `M${x},${y+size/2} L${x},${y-size/2} L${x+size/3},${y-size/2} L${x+size/3},${y} L${x},${y}`,
        'R': `M${x},${y+size/2} L${x},${y-size/2} L${x+size/3},${y-size/2} L${x+size/3},${y} L${x},${y} L${x+size/3},${y+size/2}`,
        'S': `M${x+size/3},${y-size/2} L${x},${y-size/2} L${x},${y} L${x+size/3},${y} L${x+size/3},${y+size/2} L${x},${y+size/2}`,
        'T': `M${x},${y-size/2} L${x+size/3},${y-size/2} M${x+size/6},${y-size/2} L${x+size/6},${y+size/2}`,
        'U': `M${x},${y-size/2} L${x},${y+size/2} L${x+size/3},${y+size/2} L${x+size/3},${y-size/2}`,
        'V': `M${x},${y-size/2} L${x+size/6},${y+size/2} L${x+size/3},${y-size/2}`,
        'W': `M${x},${y-size/2} L${x},${y+size/2} L${x+size/6},${y} L${x+size/3},${y+size/2} L${x+size/3},${y-size/2}`,
        'X': `M${x},${y-size/2} L${x+size/3},${y+size/2} M${x+size/3},${y-size/2} L${x},${y+size/2}`,
        'Y': `M${x},${y-size/2} L${x+size/6},${y} L${x+size/3},${y-size/2} M${x+size/6},${y} L${x+size/6},${y+size/2}`,
        'Z': `M${x},${y-size/2} L${x+size/3},${y-size/2} L${x},${y+size/2} L${x+size/3},${y+size/2}`,
        ' ': '' // Space
    };
    
    return paths[letter] || `M${x},${y} L${x+size/4},${y}`; // Default line for unknown characters
}

/**
 * Initialize DrawSVG animation (if plugin available)
 */
function initDrawSVGAnimation(svgId) {
    const svg = document.getElementById(svgId);
    if (!svg) return;
    
    const paths = svg.querySelectorAll('.letter-path');
    const fallbackText = svg.querySelector('.fallback-text');
    
    // Set initial state
    gsap.set(paths, { drawSVG: 0 });
    gsap.set(svg, { opacity: 1 });
    
    // Create timeline
    const tl = gsap.timeline();
    
    // Animate each letter group
    const letterGroups = svg.querySelectorAll('.letter-group');
    
    letterGroups.forEach((group, i) => {
        const path = group.querySelector('.letter-path');
        if (!path) return;
        
        // Create clones with different colors and stroke widths
        for (let j = 0; j < TEXT_DRAWING_CONFIG.clones; j++) {
            const clone = path.cloneNode(true);
            const strokeWidth = j < 5 ? j * 2 + 2 : 10;
            const color = TEXT_DRAWING_CONFIG.colors[j % TEXT_DRAWING_CONFIG.colors.length];
            
            clone.style.stroke = color;
            clone.style.strokeWidth = strokeWidth;
            clone.style.fill = 'none';
            clone.style.strokeLinecap = 'round';
            clone.style.strokeLinejoin = 'round';
            
            path.parentNode.insertBefore(clone, path);
            
            // Animate clone
            tl.to(clone, {
                duration: TEXT_DRAWING_CONFIG.duration,
                drawSVG: '0% 100%',
                ease: 'power2.inOut'
            }, i * TEXT_DRAWING_CONFIG.letterDelay + j * TEXT_DRAWING_CONFIG.stagger);
        }
        
        // Animate original path
        tl.to(path, {
            duration: TEXT_DRAWING_CONFIG.duration,
            drawSVG: '0% 100%',
            ease: 'power2.inOut'
        }, i * TEXT_DRAWING_CONFIG.letterDelay + TEXT_DRAWING_CONFIG.clones * TEXT_DRAWING_CONFIG.stagger);
    });
    
    // Show fallback text at the end
    tl.to(fallbackText, {
        opacity: 1,
        duration: 0.3,
        ease: 'none'
    }, '>-=0.5');
}

/**
 * Fallback animation without DrawSVG plugin
 */
function initFallbackAnimation(svgId) {
    const svg = document.getElementById(svgId);
    if (!svg) return;
    
    const fallbackText = svg.querySelector('.fallback-text');
    const paths = svg.querySelectorAll('.letter-path');
    
    // Hide paths and show text with typing effect
    gsap.set(paths, { opacity: 0 });
    gsap.set(svg, { opacity: 1 });
    
    // Typing animation
    const text = fallbackText.textContent;
    fallbackText.textContent = '';
    gsap.set(fallbackText, { opacity: 1 });
    
    const tl = gsap.timeline();
    
    for (let i = 0; i <= text.length; i++) {
        tl.to(fallbackText, {
            duration: 0.05,
            ease: 'none',
            onComplete: function() {
                fallbackText.textContent = text.substring(0, i);
            }
        });
    }
}

// Re-initialize when new content is loaded (for dynamic content)
if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        const newHeadings = node.querySelectorAll('.gsap-animation-heading:not(.gsap-text-processed)');
                        if (newHeadings.length > 0 && typeof gsap !== 'undefined') {
                            setTimeout(initTextDrawingAnimations, 100);
                        }
                    }
                });
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}
