[{"key": "group_6178d18760627", "title": "Listing Details", "fields": [{"key": "field_6178d1c1aa5c9", "label": "Price", "name": "price", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": "", "max": "", "step": ""}, {"key": "field_6178eea05fc2d", "label": "Bedrooms", "name": "bedrooms", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": "", "max": "", "step": ""}, {"key": "field_6178f212b5eea", "label": "Bathrooms", "name": "bathrooms", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": "", "max": "", "step": ""}, {"key": "field_6178f238b5eeb", "label": "Square Feet", "name": "square_feet", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": "", "max": "", "step": ""}, {"key": "field_6278d221c2871", "label": "Listing Type", "name": "listing_type", "type": "taxonomy", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "taxonomy": "listing_type", "field_type": "radio", "allow_null": 0, "add_term": 0, "save_terms": 1, "load_terms": 1, "return_format": "id", "multiple": 0}, {"key": "field_6179032926aaf", "label": "Property Type", "name": "property_type", "type": "taxonomy", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "taxonomy": "property_types", "field_type": "checkbox", "add_term": 1, "save_terms": 1, "load_terms": 1, "return_format": "id", "multiple": 0, "allow_null": 0}, {"key": "field_61790366445d5", "label": "Neighborhood", "name": "neighborhood", "type": "taxonomy", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "taxonomy": "locations", "field_type": "radio", "allow_null": 0, "add_term": 1, "save_terms": 1, "load_terms": 1, "return_format": "id", "multiple": 0}, {"key": "field_6179e32d54044", "label": "Amenities", "name": "amenities", "type": "taxonomy", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "taxonomy": "amenities", "field_type": "checkbox", "add_term": 1, "save_terms": 1, "load_terms": 1, "return_format": "object", "multiple": 0, "allow_null": 0}, {"key": "field_6179e238623ef", "label": "Video", "name": "video", "type": "oembed", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "width": "", "height": ""}, {"key": "field_61790e58132f0", "label": "Location", "name": "location", "type": "google_map", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "center_lat": "", "center_lng": "", "zoom": "", "height": ""}], "location": [[{"param": "post_type", "operator": "==", "value": "listing"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 1}]