<?php
// <PERSON><PERSON>t to fix permalinks in WordPress demo content XML

// Configuration
$input_file = 'demo-content.xml';
$output_file = 'demo-content-fixed.xml';
$old_domain = 'https://energeticthemes.com/realome';
$new_domain = 'https://jarvetipu.site.test'; // Leave empty to use relative URLs

// Read the XML file
$xml_content = file_get_contents($input_file);

// Replace the base URLs
$xml_content = str_replace(
    '<wp:base_site_url>https://energeticthemes.com/</wp:base_site_url>',
    '<wp:base_site_url>' . ($new_domain ? $new_domain : '') . '</wp:base_site_url>',
    $xml_content
);

$xml_content = str_replace(
    '<wp:base_blog_url>https://energeticthemes.com/realome</wp:base_blog_url>',
    '<wp:base_blog_url>' . ($new_domain ? $new_domain : '') . '</wp:base_blog_url>',
    $xml_content
);

// Replace all instances of the old domain in GUIDs
$xml_content = preg_replace(
    '/<guid isPermaLink="false">' . preg_quote($old_domain, '/') . '([^<]+)<\/guid>/',
    '<guid isPermaLink="false">' . ($new_domain ? $new_domain : '') . '$1</guid>',
    $xml_content
);

// Save the modified content
file_put_contents($output_file, $xml_content);

echo "XML file has been processed. New file saved as: $output_file\n";
echo "Please use this new file for importing your content.\n"; 