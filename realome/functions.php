<?php
/**
 * Realome functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package Realome
 * @since Realome 1.0
 */

if ( ! function_exists( 'realome_support' ) ) :

	/**
	 * Sets up theme defaults and registers support for various WordPress features.
	 *
	 * @since Realome 1.0
	 *
	 * @return void
	 */
	function realome_support() {

		// Add support for block styles.
		add_theme_support( 'wp-block-styles' );

		// Add support for WooCommerce.
		add_theme_support( 'woocommerce' );

	// Add Customizer support for block themes
	add_theme_support( 'customize-selective-refresh-widgets' );
	add_theme_support( 'custom-logo' );
	add_theme_support( 'custom-header' );
	add_theme_support( 'custom-background' );

		// Enqueue editor styles.
		add_editor_style(
			array(
				'style.css',
				'/build/style-index.css',
			)
		);

		add_image_size( 'square-medium', 1000, 1000, true );
		add_image_size( 'square-small', 500, 500, true );

		add_image_size( 'landscape', 1300, 975, true );
		add_image_size( 'portrait', 1300, 1736, true );

		add_image_size( 'landscape-medium', 1000, 750, true );
		add_image_size( 'portrait-medium', 1000, 1335, true );

		add_image_size( 'landscape-small', 632, 474, true );
		add_image_size( 'portrait-small', 632, 844, true );

	}

endif;

add_action( 'after_setup_theme', 'realome_support' );

/**
 * Make custom sizes selectable from WordPress admin.
 *
 * @param array $sizes images sizes.
 */
function realome_custom_image_sizes( $sizes ) {
	return array_merge(
		$sizes,
		array(
			'square-medium'    => __( 'Square Medium', 'realome' ),
			'square-small'     => __( 'Square Small', 'realome' ),
			'landscape'        => __( 'Landscape', 'realome' ),
			'landscape-medium' => __( 'Landscape Medium', 'realome' ),
			'landscape-small'  => __( 'Landscape Small', 'realome' ),
			'portrait'         => __( 'Portrait', 'realome' ),
			'portrait-medium'  => __( 'Portrait Medium', 'realome' ),
			'portrait-small'   => __( 'Portrait Small', 'realome' ),
		)
	);
}
add_filter( 'image_size_names_choose', 'realome_custom_image_sizes' );

if ( ! function_exists( 'realome_scripts' ) ) :

	/**
	 * Enqueue scripts and styles.
	 *
	 * @since Realome 1.0
	 *
	 * @return void
	 */
	function realome_scripts() {

		// Register theme stylesheet.
		$theme_version  = wp_get_theme()->get( 'Version' );
		$version_string = is_string( $theme_version ) ? $theme_version : false;
		wp_register_style(
			'realome-style',
			get_template_directory_uri() . '/style.css',
			array(),
			$version_string
		);

		// Add styles inline.
		wp_add_inline_style( 'realome-style', realome_get_font_face_styles() );

		// Add animation styles inline.
		wp_add_inline_style( 'realome-style', realome_get_animation_styles() );

		// Add gallery styles inline.
		wp_add_inline_style( 'realome-style', realome_get_gallery_styles() );

		// Add text drawing styles inline.
		wp_add_inline_style( 'realome-style', realome_get_text_drawing_styles() );

		// Enqueue theme stylesheet.
		wp_enqueue_style( 'realome-style' );

		// Register theme stylesheet.
		wp_register_style(
			'realome-theme-style',
			get_template_directory_uri() . '/build/style-index.css',
			array(),
			$version_string
		);

		// Enqueue theme stylesheet.
		wp_enqueue_style( 'realome-theme-style' );

		// Register material icons stylesheet.
		wp_register_style(
			'material-icons',
			'https://fonts.googleapis.com/icon?family=Material+Icons+Outlined',
			array(),
			'1.0.0'
		);
		wp_enqueue_style( 'material-icons' );

		// Responsive embeds script.
		wp_enqueue_script(
			'realome-responsive-embeds-script',
			get_template_directory_uri() . '/assets/js/responsive-embeds.js',
			array(),
			wp_get_theme()->get( 'Version' ),
			$version_string
		);

		// GSAP Core Library
		wp_enqueue_script(
			'gsap-core',
			'https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js',
			array(),
			'3.12.2',
			true
		);

		// GSAP DrawSVG Plugin (trial version for text effects)
		wp_enqueue_script(
			'gsap-drawsvg',
			'https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/DrawSVGPlugin.min.js',
			array('gsap-core'),
			'3.12.2',
			true
		);

		// Theme animations script (buttons only)
		wp_enqueue_script(
			'realome-animations',
			get_template_directory_uri() . '/assets/js/animations.js',
			array('gsap-core'),
			$version_string,
			true
		);

		// Text drawing animation script
		wp_enqueue_script(
			'realome-text-drawing',
			get_template_directory_uri() . '/assets/js/text-drawing.js',
			array('gsap-core', 'gsap-drawsvg'),
			$version_string,
			true
		);

		// Gallery lightbox script
		wp_enqueue_script(
			'realome-gallery',
			get_template_directory_uri() . '/assets/js/gallery.js',
			array(),
			$version_string,
			true
		);
	}

endif;

add_action( 'wp_enqueue_scripts', 'realome_scripts' );

if ( ! function_exists( 'realome_editor_styles' ) ) :

	/**
	 * Enqueue editor styles.
	 *
	 * @since Realome 1.0
	 *
	 * @return void
	 */
	function realome_inline_editor_styles() {

		// Add styles inline.
		wp_add_inline_style( 'wp-block-library', realome_get_font_face_styles() );
		wp_add_inline_style( 'wp-block-library', realome_editor_styles() );
	}

endif;

add_action( 'admin_init', 'realome_inline_editor_styles' );


if ( ! function_exists( 'realome_get_font_face_styles' ) ) :

	/**
	 * Get font face styles.
	 * Called by functions realome_scripts() and realome_editor_styles() above.
	 *
	 * @since Realome 1.0
	 *
	 * @return string
	 */
	function realome_get_font_face_styles() {

		return "
		@font-face{
			font-family: 'Inter';
			src: url('" . get_theme_file_uri( 'assets/fonts/inter-roman.woff2' ) . "') format('woff2');
			font-style: 400;
			font-weight: normal;
			font-display: swap;

		}

		@font-face{
			font-family: 'Inter';
			src: url('" . get_theme_file_uri( 'assets/fonts/Inter-medium.woff2' ) . "') format('woff2');
			font-style: normal;
			font-weight: 500;
			font-display: swap;
		}

		@font-face{
			font-family: 'Inter';
			src: url('" . get_theme_file_uri( 'assets/fonts/inter-semibold.woff2' ) . "') format('woff2');
			font-style: normal;
			font-weight: 600;
			font-display: swap;
		}

		@font-face{
			font-family: 'Inter';
			src: url('" . get_theme_file_uri( 'assets/fonts/inter-italic.woff2' ) . "') format('woff2');
			font-style: italic;
			font-weight: normal;
			font-display: swap;
		}

		/* fallback */
		@font-face {
		  font-family: 'Material Icons Outlined';
		  font-style: normal;
		  font-weight: 400;
		  src: url(https://fonts.gstatic.com/s/materialiconsoutlined/v105/gok-H7zzDkdnRel8-DQ6KAXJ69wP1tGnf4ZGhUce.woff2) format('woff2');
		}
		
		.material-icons-outlined {
		  font-family: 'Material Icons Outlined';
		  font-weight: normal;
		  font-style: normal;
		  font-size: 24px;
		  line-height: 1;
		  letter-spacing: normal;
		  text-transform: none;
		  display: inline-block;
		  white-space: nowrap;
		  word-wrap: normal;
		  direction: ltr;
		  -webkit-font-feature-settings: 'liga';
		  -webkit-font-smoothing: antialiased;
		}

		";

	}

endif;

if ( ! function_exists( 'realome_preload_webfonts' ) ) :

	/**
	 * Preloads the main web font to improve performance.
	 *
	 * Only the main web font (font-style: normal) is preloaded here since that font is always relevant (it is used
	 * on every heading, for example). The other font is only needed if there is any applicable content in italic style,
	 * and therefore preloading it would in most cases regress performance when that font would otherwise not be loaded
	 * at all.
	 *
	 * @since Realome 1.0
	 *
	 * @return void
	 */
	function realome_preload_webfonts() {
		?>
		<link rel="preload" href="<?php echo esc_url( get_theme_file_uri( 'assets/fonts/inter-roman.woff2' ) ); ?>" as="font" type="font/woff2" crossorigin>
		<?php
	}

endif;

add_action( 'wp_head', 'realome_preload_webfonts' );

if ( ! function_exists( 'realome_get_animation_styles' ) ) :

	/**
	 * Get animation styles for GSAP animations.
	 * Called by function realome_scripts() above.
	 *
	 * @since Realome 1.0
	 *
	 * @return string
	 */
	function realome_get_animation_styles() {

		return "
		/* GSAP Button Animation Enhancements */

		/* Smooth transitions for buttons only */
		.wp-block-button__link,
		.wp-element-button,
		button,
		input[type='submit'],
		input[type='button'],
		.btn,
		a.button {
			transition: all 0.3s ease;
			transform-origin: center;
			will-change: transform, box-shadow;
		}

		/* Button focus states for accessibility */
		.wp-block-button__link:focus,
		.wp-element-button:focus,
		button:focus,
		input[type='submit']:focus,
		input[type='button']:focus {
			outline: 2px solid var(--wp--preset--color--primary, #63D070);
			outline-offset: 2px;
		}

		/* Reduce motion for users who prefer it */
		@media (prefers-reduced-motion: reduce) {
			.wp-block-button__link,
			.wp-element-button,
			button,
			input[type='submit'],
			input[type='button'],
			.btn,
			a.button {
				transition-duration: 0.01ms !important;
			}
		}

		/* Enhanced button styles with liquid effect support */
		.wp-block-button__link,
		.wp-element-button,
		button,
		input[type='submit'],
		input[type='button'],
		.btn,
		a.button {
			position: relative;
			overflow: hidden;
			border-radius: 8px;
			border: none;
			padding: 12px 24px;
			font-weight: 600;
			text-decoration: none;
			display: inline-block;
		}

		/* Button content wrapper */
		.button-content {
			position: relative;
			z-index: 2;
			pointer-events: none;
		}

		/* Liquid canvas */
		.liquid-canvas {
			position: absolute;
			top: -50px;
			left: -50px;
			right: -50px;
			bottom: -50px;
			z-index: 1;
			pointer-events: none;
			border-radius: inherit;
		}

		/* Subtle gradient overlay for non-liquid buttons */
		.wp-block-button__link:not(:has(.liquid-canvas))::before,
		.wp-element-button:not(:has(.liquid-canvas))::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
			opacity: 0;
			transition: opacity 0.3s ease;
			pointer-events: none;
		}

		.wp-block-button__link:not(:has(.liquid-canvas)):hover::before,
		.wp-element-button:not(:has(.liquid-canvas)):hover::before {
			opacity: 1;
		}
		";

	}

endif;

if ( ! function_exists( 'realome_editor_styles' ) ) :

	/**
	 * Get editor styles.
	 * Called by function realome_inline_editor_styles() above.
	 *
	 * @since realome 1.0
	 *
	 * @return string
	 */
	function realome_editor_styles() {

		return '
		.editor-styles-wrapper .block-editor-block-list__layout.is-root-container > p + h1,
		.editor-styles-wrapper .block-editor-block-list__layout.is-root-container > p + h2,
		.editor-styles-wrapper .block-editor-block-list__layout.is-root-container > p + h3,
		.editor-styles-wrapper .block-editor-block-list__layout.is-root-container > p + h4 {
			margin-top: var(--wp--custom--spacing--medium, 6rem) !important;
		}
		
		.nav-list-vertical-gap-small {
			.wp-block-navigation__container {
				row-gap: 10px;
			}
		}

		/* core block fix */
		@media only screen and (min-width: 482px) {
			.editor-styles-wrapper .edit-post-visual-editor__post-title-wrapper > .alignleft, .editor-styles-wrapper .block-editor-block-list__layout.is-root-container > .alignleft {
			    margin-right: 2em !important;
			}
			.editor-styles-wrapper .edit-post-visual-editor__post-title-wrapper > .alignright, .editor-styles-wrapper .block-editor-block-list__layout.is-root-container > .alignright {
			    margin-left: 2em !important;
			}
		
			.editor-styles-wrapper .edit-post-visual-editor__post-title-wrapper > .alignleft, .editor-styles-wrapper .block-editor-block-list__layout.is-root-container > .alignleft { 
				margin-left: calc(50% - 400px) !important;
			}
			.editor-styles-wrapper .edit-post-visual-editor__post-title-wrapper > .alignright, .editor-styles-wrapper .block-editor-block-list__layout.is-root-container > .alignright { 
				margin-right: calc(50% - 400px) !important;
			}

		}
		.wp-block-search__input {
			border-color: var(--wp--preset--color--foreground);
			border-width: 0.125rem;
			border-radius: 0.5rem;
			padding: calc(calc(0.551em + 2px) - 2px) calc(calc(1.5rem + 2px) - 2px);
		}
		.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper {
			background-color: var(--wp--preset--color--white);
			border-color: var(--wp--preset--color--foreground);
			border-width: 0.125rem;
			border-radius: 0.5rem;
		}
		.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper input.wp-block-search__input {
			padding: calc(calc(0.551em + 2px) - 2px) calc(calc(1.5rem + 2px) - 2px);
		}
		
		table.mce-item-table {
		    border-collapse: collapse;
		    width: 100%;
		}
		.mce-item-table thead {
			border-bottom: 3px solid;
		}
		.mce-item-table tfoot {
			border-top: 3px solid;
		}
		.mce-item-table td,
		.mce-item-table th {
			padding: 0.5em;
			border: 1px solid;
			word-break: normal;
		}
		.mce-item-table figcaption {
			color: #555;
			font-size: 13px;
			text-align: center;
		}
		.is-dark-theme .mce-item-table figcaption {
			color: rgba(255, 255, 255, 0.65);
		}
		.wp-block-freeform.block-library-rich-text__tinymce a {
			color: var(--wp--preset--color--foreground);
		}
		';

	}

endif;

if ( ! function_exists( 'realome_get_gallery_styles' ) ) :

	/**
	 * Get gallery and lightbox styles.
	 * Called by function realome_scripts() above.
	 *
	 * @since Realome 1.0
	 *
	 * @return string
	 */
	function realome_get_gallery_styles() {

		return "
		/* Realome Gallery & Lightbox Styles */

		/* Enhanced Gallery Styles */
		.realome-enhanced-gallery {
			gap: 1rem;
		}

		.realome-enhanced-gallery img {
			border-radius: 8px;
			transition: transform 0.3s ease, box-shadow 0.3s ease;
			cursor: pointer;
		}

		.realome-enhanced-gallery img:hover {
			box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
		}

		.realome-enhanced-image img {
			cursor: pointer;
			transition: transform 0.3s ease, box-shadow 0.3s ease;
		}

		.realome-enhanced-image img:hover {
			transform: scale(1.02);
			box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
		}

		/* Property Gallery Grid Layout */
		.property-showcase.property-grid-layout {
			display: grid;
			grid-template-columns: 2fr 1fr 1fr;
			grid-template-rows: 1fr 1fr;
			gap: 0.5rem;
			max-height: 400px;
		}

		.property-grid-layout .featured-image {
			grid-row: 1 / 3;
			grid-column: 1;
		}

		.property-grid-layout .grid-image {
			position: relative;
		}

		.property-grid-layout .featured-image img,
		.property-grid-layout .grid-image img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}

		/* More images overlay */
		.more-images-overlay {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0, 0, 0, 0.7);
			display: flex;
			align-items: center;
			justify-content: center;
			color: white;
			font-weight: 600;
			font-size: 1.2rem;
			cursor: pointer;
			border-radius: 8px;
		}

		/* Lightbox Styles */
		.realome-lightbox {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 9999;
			opacity: 0;
			visibility: hidden;
			transition: opacity 0.3s ease, visibility 0.3s ease;
		}

		.realome-lightbox.active {
			opacity: 1;
			visibility: visible;
		}

		.lightbox-overlay {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: rgba(0, 0, 0, 0.9);
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 2rem;
		}

		.lightbox-container {
			position: relative;
			max-width: 90vw;
			max-height: 90vh;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.lightbox-content {
			position: relative;
			text-align: center;
		}

		.lightbox-image {
			max-width: 100%;
			max-height: 80vh;
			object-fit: contain;
			border-radius: 8px;
			box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
		}

		.lightbox-info {
			margin-top: 1rem;
			color: white;
		}

		.lightbox-counter {
			font-size: 0.9rem;
			opacity: 0.8;
		}

		.lightbox-caption {
			margin: 0.5rem 0 0 0;
			font-size: 1rem;
			font-weight: 500;
		}

		/* Navigation Buttons */
		.lightbox-close,
		.lightbox-prev,
		.lightbox-next {
			position: absolute;
			background: rgba(255, 255, 255, 0.1);
			border: none;
			color: white;
			font-size: 2rem;
			cursor: pointer;
			padding: 1rem;
			border-radius: 50%;
			transition: background 0.3s ease;
			backdrop-filter: blur(10px);
		}

		.lightbox-close:hover,
		.lightbox-prev:hover,
		.lightbox-next:hover {
			background: rgba(255, 255, 255, 0.2);
		}

		.lightbox-close {
			top: 2rem;
			right: 2rem;
			font-size: 1.5rem;
		}

		.lightbox-prev {
			left: 2rem;
			top: 50%;
			transform: translateY(-50%);
		}

		.lightbox-next {
			right: 2rem;
			top: 50%;
			transform: translateY(-50%);
		}

		/* Mobile Responsive */
		@media (max-width: 768px) {
			.lightbox-overlay {
				padding: 1rem;
			}

			.lightbox-close {
				top: 1rem;
				right: 1rem;
				padding: 0.5rem;
				font-size: 1.2rem;
			}

			.lightbox-prev,
			.lightbox-next {
				padding: 0.5rem;
				font-size: 1.5rem;
			}

			.lightbox-prev {
				left: 1rem;
			}

			.lightbox-next {
				right: 1rem;
			}

			.property-showcase.property-grid-layout {
				grid-template-columns: 1fr 1fr;
				grid-template-rows: 200px 100px 100px;
				max-height: 420px;
			}

			.property-grid-layout .featured-image {
				grid-row: 1;
				grid-column: 1 / 3;
			}
		}

		/* Accessibility */
		@media (prefers-reduced-motion: reduce) {
			.realome-enhanced-gallery img,
			.realome-enhanced-image img,
			.realome-lightbox {
				transition: none !important;
			}
		}

		/* Focus states for accessibility */
		.realome-enhanced-gallery img:focus,
		.realome-enhanced-image img:focus {
			outline: 2px solid var(--wp--preset--color--primary, #63D070);
			outline-offset: 2px;
		}
		";

	}

endif;

if ( ! function_exists( 'realome_get_text_drawing_styles' ) ) :

	/**
	 * Get text drawing animation styles.
	 * Called by function realome_scripts() above.
	 *
	 * @since Realome 1.0
	 *
	 * @return string
	 */
	function realome_get_text_drawing_styles() {

		return "
		/* Realome Text Drawing Animation Styles */

		/* Text Drawing Container */
		.gsap-text-drawing-container {
			display: inline-block;
			width: 100%;
			overflow: visible;
		}

		.text-drawing-svg {
			max-width: 100%;
			height: auto;
			overflow: visible;
			opacity: 0;
		}

		/* Letter Paths */
		.letter-path {
			stroke: var(--wp--preset--color--primary, #63D070);
			stroke-width: 3px;
			stroke-linecap: round;
			stroke-linejoin: round;
			stroke-miterlimit: 10;
			fill: none;
		}

		/* Fallback Text */
		.fallback-text {
			font-family: inherit;
			fill: currentColor;
			opacity: 0;
		}

		/* Animation Heading Base Styles */
		.gsap-animation-heading {
			position: relative;
			overflow: visible;
		}

		.gsap-animation-heading.gsap-text-processed {
			/* Ensure container maintains proper spacing */
			min-height: 1em;
		}

		/* Responsive Design */
		@media (max-width: 768px) {
			.text-drawing-svg {
				max-width: 100%;
			}

			.letter-path {
				stroke-width: 2px;
			}
		}

		/* Reduced Motion Support */
		@media (prefers-reduced-motion: reduce) {
			.text-drawing-svg {
				opacity: 1 !important;
			}

			.fallback-text {
				opacity: 1 !important;
			}

			.letter-path {
				opacity: 0 !important;
			}
		}

		/* Color Variations for Different Heading Levels */
		h1.gsap-animation-heading .letter-path {
			stroke: var(--wp--preset--color--primary, #63D070);
		}

		h2.gsap-animation-heading .letter-path {
			stroke: var(--wp--preset--color--secondary, #EEC543);
		}

		h3.gsap-animation-heading .letter-path {
			stroke: var(--wp--preset--color--tertiary, #8763cf);
		}

		/* Loading State */
		.gsap-animation-heading:not(.gsap-text-processed) {
			opacity: 0.7;
		}

		/* Ensure proper text alignment */
		.gsap-text-drawing-container svg {
			display: block;
			margin: 0 auto;
		}

		.text-align-center .gsap-text-drawing-container,
		.has-text-align-center .gsap-text-drawing-container {
			text-align: center;
		}

		.text-align-left .gsap-text-drawing-container,
		.has-text-align-left .gsap-text-drawing-container {
			text-align: left;
		}

		.text-align-right .gsap-text-drawing-container,
		.has-text-align-right .gsap-text-drawing-container {
			text-align: right;
		}
		";

	}

endif;

// Load the TGMPA class.
require get_parent_theme_file_path( '/inc/plugins.php' );

// Add block patterns.
require get_template_directory() . '/inc/block-patterns.php';

/**
 * Disable comments functionality
 */
function realome_disable_comments() {
    // Close comments on the front-end
    add_filter('comments_open', '__return_false', 20, 2);
    add_filter('pings_open', '__return_false', 20, 2);

    // Hide existing comments
    add_filter('comments_array', '__return_empty_array', 10, 2);

    // Remove comments page from admin menu
    add_action('admin_menu', function() {
        remove_menu_page('edit-comments.php');
    }, 999);

    // Remove comments links from admin bar
    add_action('wp_before_admin_bar_render', function() {
        global $wp_admin_bar;
        if ($wp_admin_bar) {
            $wp_admin_bar->remove_menu('comments');
        }
    });

    // Remove comments from post and pages
    add_action('init', function() {
        remove_post_type_support('post', 'comments');
        remove_post_type_support('page', 'comments');
    });

    // Remove comments from admin menu
    add_action('admin_init', function() {
        global $menu;
        if (is_array($menu)) {
            foreach ($menu as $key => $item) {
                if (isset($item[2]) && $item[2] === 'edit-comments.php') {
                    unset($menu[$key]);
                }
            }
        }
    });
}
add_action('init', 'realome_disable_comments');

/**
 * Enable Customizer for block themes
 */
function realome_enable_customizer() {
    // Remove the theme.json to allow Customizer (optional - only if you want full Customizer functionality)
    // This is commented out by default to preserve block theme functionality
    // remove_theme_support( 'block-templates' );

    // Ensure Customizer is available
    add_action( 'customize_register', function( $wp_customize ) {
        // Add a basic section if none exist
        if ( empty( $wp_customize->sections() ) ) {
            $wp_customize->add_section( 'realome_basic_settings', array(
                'title'    => __( 'Basic Theme Settings', 'realome' ),
                'priority' => 30,
            ) );
        }
    } );
}
add_action( 'after_setup_theme', 'realome_enable_customizer' );

add_filter( 'gform_required_legend', '__return_empty_string' );